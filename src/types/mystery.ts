// Updated types to match Firestore schema
export interface Evidence {
  item: string;
  location: string;
  significance: string;
}

export interface Contradiction {
  contradiction: string;
  explanation: string;
}

export interface Suspect {
  name: string;
  motive: string;
  means: string;
  opportunity: string;
  specialized_knowledge: string;
  evidence_against: string;
  evidence_for: string;
}

export interface TimelineAnalysis {
  crime_window: string;
  key_events: string[];
}

export interface Reconstruction {
  actual_sequence: string;
  staging_process: string;
  required_expertise: string;
}

export interface Resolution {
  killer: string;
  method: string;
  motive: string;
  key_evidence: string;
}

export interface StoryElements {
  characters: string[];
  objects: string[];
  locations: string[];
}

// Main Mystery interface matching Firestore schema
export interface FirestoreMystery {
  Title: string;
  FullText: string;
  Summary: string;
  Evidence: Evidence[];
  Contradictions: Contradiction[];
  Suspects: Suspect[];
  TimelineAnalysis: TimelineAnalysis;
  Reconstruction: Reconstruction;
  Resolution: Resolution;
  StoryElements: StoryElements;
}

// Legacy interface for backward compatibility with existing components
export interface LegacySuspect {
  id: string;
  name: string;
  description: string;
  imageHint: string; // For data-ai-hint, e.g., "vintage portrait"
}

export interface LegacyCrimeExplanation {
  motive: string;
  method: string;
  keyEvidence: string;
}

export interface Mystery {
  id: string;
  title: string;
  story: string;
  suspects: LegacySuspect[];
  killerId: string;
  explanation: LegacyCrimeExplanation;
}
