import { ReactNode } from 'react';

interface ArticleLayoutProps {
  mainContent: ReactNode;
  sidebarContent?: ReactNode;
  className?: string;
}

export function ArticleLayout({ mainContent, sidebarContent, className = '' }: ArticleLayoutProps) {
  if (!sidebarContent) {
    // Single column layout when no sidebar
    return (
      <div className={`w-full ${className}`}>
        <div className="newspaper-main-article">
          {mainContent}
        </div>
      </div>
    );
  }

  // Two-column layout with sidebar
  return (
    <div className={`newspaper-article-container ${className}`}>
      <main className="newspaper-main-article">
        {mainContent}
      </main>
      <aside className="newspaper-sidebar">
        {sidebarContent}
      </aside>
    </div>
  );
}
