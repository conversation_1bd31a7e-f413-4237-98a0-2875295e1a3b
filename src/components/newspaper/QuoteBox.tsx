import { ReactNode } from 'react';

interface QuoteBoxProps {
  children: ReactNode;
  attribution?: string;
  className?: string;
}

export function QuoteBox({ children, attribution, className = '' }: QuoteBoxProps) {
  return (
    <div className={`newspaper-quote-box ${className}`}>
      {children}
      {attribution && (
        <div className="mt-2">
          <strong>— {attribution}</strong>
        </div>
      )}
    </div>
  );
}
