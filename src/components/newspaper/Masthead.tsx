interface MastheadProps {
  title: string;
  tagline: string;
  date?: string;
  price?: string;
}

export function Masthead({ title, tagline, date, price }: MastheadProps) {
  const currentDate = date || new Date().toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  return (
    <header className="newspaper-masthead">
      <h1 className="newspaper-name">{title}</h1>
      <p className="newspaper-tagline">{tagline}</p>
      <div className="newspaper-date-price">
        <span>{currentDate}</span>
        {price && <span>Price: {price}</span>}
      </div>
    </header>
  );
}
