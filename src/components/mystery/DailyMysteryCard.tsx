import type { Mystery } from '@/types/mystery';
import { Byline } from '@/components/newspaper';
import { MarkdownRenderer } from '@/components/ui/MarkdownRenderer';

interface DailyMysteryCardProps {
  mystery: Mystery;
}

export function DailyMysteryCard({ mystery }: DailyMysteryCardProps) {
  return (
    <article className="w-full">
      <header className="mb-6">
        <h1 className="newspaper-headline text-4xl md:text-5xl mb-4">
          {mystery.title}
        </h1>
        <h2 className="newspaper-subheadline text-xl md:text-2xl mb-4">
          A mysterious case unfolds in our city, demanding the attention of keen detectives
        </h2>
        <Byline
          author="Detective Bureau"
          title="Crime Reporter"
          className="mb-6"
        />
      </header>

      <MarkdownRenderer
        content={mystery.story}
        enableDropCap={true}
      />
    </article>
  );
}
