import type { LegacyCrimeExplanation as CrimeExplanationType } from '@/types/mystery';
import { QuoteBox, Byline } from '@/components/newspaper';
import { CheckCircle, AlertTriangle } from 'lucide-react';
import { MarkdownRenderer } from '@/components/ui/MarkdownRenderer';

interface CrimeExplanationProps {
  explanation: CrimeExplanationType;
  killerName: string;
  isCorrectGuess: boolean | null;
  accusedName?: string | null;
}

export function CrimeExplanation({ explanation, killerName, isCorrectGuess, accusedName }: CrimeExplanationProps) {
  return (
    <article className="w-full mt-8 border-t-4 border-primary pt-8">
      <header className="mb-6">
        <h2 className="newspaper-headline text-3xl md:text-4xl mb-4">
          The Whole Story Unveiled
        </h2>
        <h3 className="newspaper-subheadline text-lg md:text-xl mb-4">
          Detective work concludes as the truth behind the mysterious case comes to light
        </h3>
        <Byline
          author="Detective Bureau"
          title="Final Report"
          className="mb-6"
        />

        {isCorrectGuess !== null && (
          <div className="mb-6 p-4 border-l-4 border-primary bg-gray-50">
            <div className="flex items-center">
              {isCorrectGuess ? (
                <>
                  <CheckCircle className="h-6 w-6 text-green-600 mr-3" />
                  <span className="text-green-700 font-semibold newspaper-text">
                    Brilliant deduction! You correctly identified {killerName} as the culprit.
                  </span>
                </>
              ) : (
                <>
                  <AlertTriangle className="h-6 w-6 text-red-600 mr-3" />
                  <span className="text-red-700 font-semibold newspaper-text">
                    {accusedName ? `Your accusation of ${accusedName} was incorrect. The real culprit was ${killerName}.` : `The real culprit was ${killerName}.`}
                  </span>
                </>
              )}
            </div>
          </div>
        )}
      </header>

      <div className="space-y-6">
        <div>
          <h4 className="newspaper-sidebar-headline text-xl mb-3">The Motive</h4>
          <MarkdownRenderer content={explanation.motive} />
        </div>

        <QuoteBox attribution="Detective's Analysis">
          The method employed in this case reveals a calculated approach that required both opportunity and specific knowledge.
        </QuoteBox>

        <div>
          <h4 className="newspaper-sidebar-headline text-xl mb-3">The Method</h4>
          <MarkdownRenderer content={explanation.method} />
        </div>

        <div>
          <h4 className="newspaper-sidebar-headline text-xl mb-3">Key Evidence</h4>
          <MarkdownRenderer content={explanation.keyEvidence} />
        </div>

        <div className="border-t border-gray-300 pt-6 mt-8">
          <p className="text-sm italic text-gray-600">
            Case closed. Justice has been served, and the truth has prevailed once again in our fair city.
          </p>
        </div>
      </div>
    </article>
  );
}
