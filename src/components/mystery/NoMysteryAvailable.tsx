import { Byline } from '@/components/newspaper';
import { FileX, Clock, Skull } from 'lucide-react';

interface NoMysteryAvailableProps {
  error?: string;
  isConnectionError?: boolean;
}

export function NoMysteryAvailable({ error, isConnectionError = false }: NoMysteryAvailableProps) {
  const getIcon = () => {
    if (isConnectionError) return <Clock className="h-16 w-16 text-primary/60" />;
    return <FileX className="h-16 w-16 text-primary/60" />;
  };

  const getTitle = () => {
    if (isConnectionError) return "The Investigation is Delayed";
    return "No Murders Today... Yet";
  };

  const getMessage = () => {
    if (isConnectionError) {
      return "Our detectives are having trouble accessing the case files. Please check your connection and try again.";
    }
    return "The case files are empty today. It seems the criminal underworld is taking a well-deserved rest.";
  };

  const getSubMessage = () => {
    if (isConnectionError) {
      return "The mystery archives should be accessible shortly.";
    }
    return "Return tomorrow when fresh mysteries await your keen detective skills.";
  };

  return (
    <article className="w-full">
      <header className="text-center mb-8">
        <div className="flex justify-center mb-4">
          {getIcon()}
        </div>
        <h1 className="newspaper-headline text-3xl md:text-4xl mb-4">
          {getTitle()}
        </h1>
        <h2 className="newspaper-subheadline text-lg md:text-xl mb-4">
          {isConnectionError ? "Technical difficulties interrupt our daily investigation" : "A rare peaceful day in our city"}
        </h2>
        <Byline
          author="City Desk"
          title="News Reporter"
          className="mb-6"
        />
      </header>

      <div className="newspaper-text">
        <p className="newspaper-drop-cap">
          {getMessage()}
        </p>

        <p className="mt-4">
          {getSubMessage()}
        </p>

        {error && (
          <div className="mt-6 p-4 bg-gray-100 border-l-4 border-red-500">
            <h4 className="newspaper-sidebar-headline text-base mb-2">Technical Details</h4>
            <p className="text-sm text-red-700">
              {error}
            </p>
          </div>
        )}

        <div className="mt-8 pt-6 border-t border-gray-300 text-center">
          <div className="flex items-center justify-center space-x-2 text-muted-foreground mb-4">
            <Skull className="h-5 w-5" />
            <span className="text-sm italic">
              "In the absence of crime, the detective's mind grows sharper."
            </span>
            <Skull className="h-5 w-5" />
          </div>
          <p className="text-xs text-muted-foreground italic">
            — Anonymous Detective, 1892
          </p>
        </div>
      </div>
    </article>
  );
}
