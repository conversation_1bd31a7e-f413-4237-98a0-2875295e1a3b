import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { ReactNode } from 'react';

interface MarkdownRendererProps {
  content: string;
  className?: string;
  enableDropCap?: boolean;
}

export function MarkdownRenderer({ content, className = '', enableDropCap = false }: MarkdownRendererProps) {
  return (
    <div className={`newspaper-markdown ${className}`}>
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        components={{
          // Custom paragraph renderer to handle drop cap for first paragraph
          p: ({ children, ...props }) => {
            const isFirstParagraph = enableDropCap && props.node?.position?.start.line === 1;
            return (
              <p 
                className={isFirstParagraph ? 'newspaper-drop-cap' : ''} 
                {...props}
              >
                {children}
              </p>
            );
          },
          // Custom link renderer to ensure proper newspaper styling
          a: ({ href, children, ...props }) => (
            <a 
              href={href} 
              target="_blank" 
              rel="noopener noreferrer"
              {...props}
            >
              {children}
            </a>
          ),
          // Custom blockquote renderer for newspaper-style quotes
          blockquote: ({ children, ...props }) => (
            <blockquote {...props}>
              {children}
            </blockquote>
          ),
          // Custom code block renderer
          pre: ({ children, ...props }) => (
            <pre {...props}>
              {children}
            </pre>
          ),
          // Custom inline code renderer
          code: ({ children, ...props }) => (
            <code {...props}>{children}</code>
          ),
          // Custom header renderers to maintain newspaper hierarchy
          h1: ({ children, ...props }) => (
            <h1 {...props}>{children}</h1>
          ),
          h2: ({ children, ...props }) => (
            <h2 {...props}>{children}</h2>
          ),
          h3: ({ children, ...props }) => (
            <h3 {...props}>{children}</h3>
          ),
          h4: ({ children, ...props }) => (
            <h4 {...props}>{children}</h4>
          ),
          h5: ({ children, ...props }) => (
            <h5 {...props}>{children}</h5>
          ),
          h6: ({ children, ...props }) => (
            <h6 {...props}>{children}</h6>
          ),
          // Custom list renderers
          ul: ({ children, ...props }) => (
            <ul {...props}>{children}</ul>
          ),
          ol: ({ children, ...props }) => (
            <ol {...props}>{children}</ol>
          ),
          li: ({ children, ...props }) => (
            <li {...props}>{children}</li>
          ),
          // Custom table renderers
          table: ({ children, ...props }) => (
            <table {...props}>{children}</table>
          ),
          thead: ({ children, ...props }) => (
            <thead {...props}>{children}</thead>
          ),
          tbody: ({ children, ...props }) => (
            <tbody {...props}>{children}</tbody>
          ),
          tr: ({ children, ...props }) => (
            <tr {...props}>{children}</tr>
          ),
          th: ({ children, ...props }) => (
            <th {...props}>{children}</th>
          ),
          td: ({ children, ...props }) => (
            <td {...props}>{children}</td>
          ),
          // Custom horizontal rule renderer
          hr: ({ ...props }) => (
            <hr {...props} />
          ),
          // Custom emphasis renderers
          strong: ({ children, ...props }) => (
            <strong {...props}>{children}</strong>
          ),
          em: ({ children, ...props }) => (
            <em {...props}>{children}</em>
          ),
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
}
