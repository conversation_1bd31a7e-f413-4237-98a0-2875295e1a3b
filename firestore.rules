rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Allow service account (admin) access to all documents
    match /{document=**} {
      // Allow read and write for authenticated requests
      // Service accounts using Firebase Admin SDK will have admin privileges
      allow read: if true;
      allow write: if request.auth != null;

      // Alternatively, you can allow all access for development/testing
      // Uncomment the line below and comment out the line above for open access
      // allow read, write: if true;
    }
  }
}