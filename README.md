# DailyDeduction 🔍

A daily murder mystery game where players solve a new case each day. Built with Next.js and Firebase.

## 🎯 Overview

DailyDeduction presents players with a fresh murder mystery every day. Players examine evidence, analyze suspects, and make accusations to solve the case. Each mystery is the same for all players on a given day, creating a shared daily challenge.

## ✨ Features

- **📅 Daily Mysteries**: New murder case every day with date-based rotation
- **🕵️ Interactive Investigation**: Read detailed mystery stories and examine suspect profiles
- **⚖️ Accusation System**: Select and accuse suspects with immediate feedback
- **💡 Detailed Resolutions**: Complete crime explanations with motive, method, and evidence
- **🎨 Mystery-Themed Design**: Elegant burgundy and gold color scheme with vintage typography
- **📱 Responsive Design**: Works seamlessly on desktop and mobile devices

## 🛠️ Technology Stack

- **Frontend**: Next.js 15.3.3 with React 18 and TypeScript
- **Database**: Firebase/Firestore for mystery data storage
- **Styling**: Tailwind CSS with custom design system
- **UI Components**: Radix UI primitives
- **AI Integration**: Google Genkit
- **Typography**: Playfair Display (headlines) + PT Sans (body)

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- Firebase project with Firestore enabled

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

3. Set up environment variables in `.env.local`:
   ```env
   NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key
   NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
   NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
   NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.firebasestorage.app
   NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
   NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id
   NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=your_measurement_id
   ```

4. Run the development server:
   ```bash
   npm run dev
   ```

5. Open [http://localhost:9002](http://localhost:9002) in your browser

## 📊 Data Structure

Mysteries are stored in Firestore with the following structure:

- **Collection**: `mysteries`
- **Document ID**: `yyyy-MM-dd` (e.g., `20250618` for June 18, 2025)
- **Schema**: See `src/types/mystery.ts` for the complete `FirestoreMystery` interface

## 🏗️ Project Structure

```
src/
├── app/                 # Next.js App Router pages
├── components/          # Reusable UI components
│   ├── mystery/        # Mystery-specific components
│   └── ui/             # General UI components
├── lib/                # Utility functions and services
├── types/              # TypeScript type definitions
└── hooks/              # Custom React hooks
```

## 🔧 Development

- **Dev Server**: `npm run dev` (runs on port 9002)
- **Build**: `npm run build`
- **Type Check**: `npm run typecheck`
- **Lint**: `npm run lint`

## 🎨 Design System

The app uses a mystery-themed design with:
- **Primary Color**: Deep burgundy (#800020)
- **Background**: Off-white (#F5F5DC)
- **Accent**: Gold (#FFD700)
- **Fonts**: Playfair Display (serif) for headlines, PT Sans for body text

## 🔄 Fallback System

If Firestore is unavailable or no mystery exists for the current date, the app automatically falls back to mock data to ensure uninterrupted gameplay.

## 📝 License

This project is part of the Firebase Studio ecosystem.
